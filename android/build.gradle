group = "com.example.video_player_flutter3"
version = "1.0"

buildscript {
    repositories {
        google()
        mavenCentral()
//        maven { url 'https://nexus.weimiaocaishang.com/repository/maven-releases/' }
        maven {
            url = uri("https://nexus.weimiaocaishang.com/repository/maven-releases/")
            credentials {
                username = "admin"
                password = "hello@1234"
            }
        }
    }

    dependencies {
        classpath("com.android.tools.build:gradle:8.3.2")
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
//        maven { url 'https://nexus.weimiaocaishang.com/repository/maven-releases/' }
        maven {
            url = uri("https://nexus.weimiaocaishang.com/repository/maven-releases/")
            credentials {
                username = "admin"
                password = "hello@1234"
            }
        }
    }
}

apply plugin: "com.android.library"

android {
    namespace = "com.example.video_player_flutter3"

    compileSdk = 35

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdk = 24
    }

    dependencies {
        testImplementation("junit:junit:4.13.2")
        testImplementation("org.mockito:mockito-core:5.0.0")
//        implementation 'com.weimiao.appsdk.android:wmvodplayersdk:1.0.0@aar'
        // implementation 'com.weimiao.appsdk.android:wmliveplayersdk-test:1.0.36'
        implementation 'com.weimiao.appsdk.android:wmliveplayersdk:1.2.8'
    }

    testOptions {
        unitTests.all {
            testLogging {
                events "passed", "skipped", "failed", "standardOut", "standardError"
                outputs.upToDateWhen { false }
                showStandardStreams = true
            }
        }
    }
}
