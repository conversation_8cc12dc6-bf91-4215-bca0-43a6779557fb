package com.example.video_player_flutter3;

import static com.tencent.rtmp.TXLiveConstants.EVT_PLAYABLE_DURATION_MS;
import static com.tencent.rtmp.TXLiveConstants.EVT_PLAY_DURATION_MS;
import static com.tencent.rtmp.TXLiveConstants.EVT_PLAY_PROGRESS_MS;
import static com.tencent.rtmp.TXLiveConstants.NET_STATUS_NET_SPEED;
import static com.tencent.rtmp.TXLiveConstants.PLAY_ERR_NET_DISCONNECT;
import static com.tencent.rtmp.TXLiveConstants.PLAY_EVT_PLAY_BEGIN;
import static com.tencent.rtmp.TXLiveConstants.PLAY_EVT_PLAY_END;
import static com.tencent.rtmp.TXLiveConstants.PLAY_EVT_PLAY_LOADING;
import static com.tencent.rtmp.TXLiveConstants.PLAY_EVT_RCV_FIRST_I_FRAME;
import static com.tencent.rtmp.TXLiveConstants.PLAY_EVT_VOD_LOADING_END;
import static com.tencent.rtmp.TXLiveConstants.PLAY_EVT_VOD_PLAY_PREPARED;

import android.content.Context;
import android.graphics.SurfaceTexture;
import android.os.Bundle;
import android.view.Surface;

import androidx.annotation.NonNull;

import com.tencent.rtmp.ITXVodPlayListener;
import com.tencent.rtmp.TXLiveConstants;
import com.tencent.rtmp.TXVodPlayConfig;
import com.tencent.rtmp.TXVodPlayer;

import java.util.HashMap;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.view.TextureRegistry;

public class VideoPlayer implements MethodChannel.MethodCallHandler {
    private static final String TAG = "VideoPlayer";

    private TXVodPlayer vodPlayer;
    private TextureRegistry.SurfaceTextureEntry textureEntry;
    private final MethodChannel methodChannel;
    private SurfaceTexture surfaceTexture;
    private Surface surface;

    /**
     * 初始化播放器
     *
     * @param context       context
     * @param textureEntry  textureEntry
     * @param methodChannel methodChannel
     */
    VideoPlayer(Context context, TextureRegistry.SurfaceTextureEntry textureEntry, MethodChannel methodChannel) {
        this.textureEntry = textureEntry;
        this.methodChannel = methodChannel;
        vodPlayer = new TXVodPlayer(context);
        setupVideoPlayer();
        this.methodChannel.setMethodCallHandler(this);
    }

    private void setupVideoPlayer() {
        surfaceTexture = textureEntry.surfaceTexture();
        surface = new Surface(surfaceTexture);
        vodPlayer.setSurface(surface);
        surfaceTexture.setDefaultBufferSize(1920, 1080);
//        vodPlayer.setRenderMode(TXLiveConstants.RENDER_MODE_FULL_FILL_SCREEN);
        vodPlayer.setRenderRotation(TXLiveConstants.RENDER_ROTATION_PORTRAIT);
        vodPlayer.setRenderMode(TXLiveConstants.RENDER_MODE_ADJUST_RESOLUTION);
        vodPlayer.setBitrateIndex(0);
        vodPlayer.setVodListener(new ITXVodPlayListenerImpl(methodChannel));
    }

    /**
     * 设置播放器额外参数
     * 如：Referer
     *
     * @param map
     */
    public void setConfig(HashMap<String, String> map) {
        TXVodPlayConfig config = new TXVodPlayConfig();
        HashMap<String, String> headers = new HashMap<>(map);
        config.setHeaders(headers);
        vodPlayer.setConfig(config);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        String method = call.method;
        switch (method) {
            case "setConfig":
                setConfig(call.argument("arg"));
                result.success(null);
                break;
            case "play":
                String url = call.argument("arg");
//                vodPlayer.startPlay(url);
                vodPlayer.startVodPlay(url);
                result.success(null);
                break;
            case "pause":
                vodPlayer.pause();
                result.success(null);
                break;
            case "dispose":
                long textureId = Long.parseLong(call.argument("arg").toString());
                dispose();
                result.success(null);
                break;
            case "resume":
                vodPlayer.resume();
                result.success(null);
                break;
            case "setAutoPlay":
                boolean autoPlay = call.argument("arg");
                vodPlayer.setAutoPlay(autoPlay);
                result.success(null);
                break;
            case "seek":
                int time = call.argument("arg");
//                float time = call.argument("arg");
                vodPlayer.seek(Float.parseFloat(String.valueOf(time / 1000)));
                result.success(null);
                break;
            case "setLoop":
                boolean loop = call.argument("arg");
                vodPlayer.setLoop(loop);
                result.success(null);
                break;
            case "setMute":
                boolean mute = call.argument("arg");
                vodPlayer.setMute(mute);
                result.success(null);
                break;
            case "setRate":
                double rate = (Double) call.argument("arg");
                vodPlayer.setRate((float) rate);
                result.success(null);
                break;
            case "setStartTime":
                int startTime = call.argument("arg");
                vodPlayer.setStartTime(new Float(startTime / 1000f));
                result.success(null);
                break;
        }
    }

    /**
     * 销毁播放器
     */
    public void dispose() {
        if (vodPlayer != null) {
            // false 代表不清除最后一帧画面
            vodPlayer.stopPlay(false);
            vodPlayer = null;
        }

        if (textureEntry != null) {
            textureEntry.release();
            textureEntry = null;
        }

        if (surfaceTexture != null) {
            surfaceTexture.release();
            surfaceTexture = null;
        }

        if (surface != null) {
            surface.release();
            surface = null;
        }
        methodChannel.setMethodCallHandler(null);
    }

    static class ITXVodPlayListenerImpl implements ITXVodPlayListener {
        private final MethodChannel methodChannel;


        ITXVodPlayListenerImpl(MethodChannel methodChannel) {
            this.methodChannel = methodChannel;
        }

        @Override
        public void onPlayEvent(TXVodPlayer txVodPlayer, int event, Bundle bundle) {
            if (event == TXLiveConstants.PLAY_EVT_PLAY_PROGRESS && txVodPlayer.isPlaying()) {
//                int bufferedMs = bundle.getInt("EVT_PLAYABLE_DURATION");
                int bufferedMs = bundle.getInt(EVT_PLAYABLE_DURATION_MS);
                int progressMs = bundle.getInt(EVT_PLAY_PROGRESS_MS);
                int totalMs = bundle.getInt(EVT_PLAY_DURATION_MS);
                HashMap<String, Object> params = new HashMap<>();
                params.put("buffered", bufferedMs);
                params.put("position", progressMs);
                params.put("duration", totalMs);
                callDartMethod("onPlayProgress", params);
            } else if (event == PLAY_EVT_PLAY_BEGIN) {
                callDartMethod("onPlayBegin", null);
            } else if (event == PLAY_EVT_PLAY_END) {
                callDartMethod("onPlayEnd", null);
                txVodPlayer.stopPlay(false);
            } else if (event == PLAY_EVT_PLAY_LOADING) {
                callDartMethod("onLoading", null);
            } else if (event == PLAY_EVT_VOD_LOADING_END) {
                callDartMethod("onLoadingEnd", null);
            } else if (event == PLAY_ERR_NET_DISCONNECT) {
                callDartMethod("onNetDisconnect", null);
            } else if (event == PLAY_EVT_VOD_PLAY_PREPARED) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("width", txVodPlayer.getWidth());
                params.put("height", txVodPlayer.getHeight());
                callDartMethod("onPrepared", params);
            } else if (event == PLAY_EVT_RCV_FIRST_I_FRAME) {
                callDartMethod("onFirstFrame", null);
            }
        }

        @Override
        public void onNetStatus(TXVodPlayer txVodPlayer, Bundle bundle) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("netSpeed", bundle.get(NET_STATUS_NET_SPEED));
            callDartMethod("onNetStatus", params);
        }

        /**
         * native -> dart
         *
         * @param method 方法名称
         * @param params 参数
         */
        private void callDartMethod(String method, HashMap<String, Object> params) {
            MainHandler.getInstance().post(new Runnable() {
                @Override
                public void run() {
                    HashMap<String, Object> resultMap = new HashMap<>();
                    if (params != null) resultMap = params;
                    methodChannel.invokeMethod(method, resultMap);
                }
            });
        }
    }
}
