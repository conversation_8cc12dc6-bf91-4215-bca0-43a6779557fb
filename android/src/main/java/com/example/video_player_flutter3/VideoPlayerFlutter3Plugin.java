package com.example.video_player_flutter3;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.wmplayersdk.license.WmBaseListener;
import com.wmplayersdk.license.WmLicense;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import io.flutter.view.TextureRegistry;

/**
 * VideoPlayerFlutter3Plugin
 */
public class VideoPlayerFlutter3Plugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    private static final String TAG = "WmAliPlayerPlugin";
    private MethodChannel channel;
    private TextureRegistry textureRegistry;
    private Context context;
    private FlutterPluginBinding binding;

    // Licence 检查标志
    private boolean isLicenceChecked = false;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        this.binding = flutterPluginBinding;
        textureRegistry = flutterPluginBinding.getTextureRegistry();
        context = flutterPluginBinding.getApplicationContext();
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "wm.io/videoPlayer/tencent");
        channel.setMethodCallHandler(this);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        String method = call.method;
        if ("setLicence".equals(method)) {
            String licenceUrl = call.argument("licenceUrl");
            String licenceKey = call.argument("licenceKey");
            initLicense(licenceUrl, licenceKey);
        } else if (method.equals("initialize")) {
            if (isLicenceChecked) {
                final long textureId = create();
                result.success(textureId);
            } else {
                result.error("LICENSE_INIT_FAILED", "License initialization failed", -1);
            }
        } else {
            result.notImplemented();
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
    }

    /**
     * 创建播放器
     */
    private Long create() {
        TextureRegistry.SurfaceTextureEntry entry = textureRegistry.createSurfaceTexture();
        String methodName = "wm.io/videoPlayer/tencent" + entry.id();
        MethodChannel methodChannel = new MethodChannel(binding.getBinaryMessenger(), methodName);
//        VideoPlayer videoPlayer = new VideoPlayer(context, entry, methodChannel);
        new VideoPlayer(context, entry, methodChannel);
//        videoPlayers.put(entry.id(), videoPlayer);
        return entry.id();
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {

    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {

    }

    @Override
    public void onDetachedFromActivity() {

    }

    /**
     * 初始化播放器 License
     *
     * @param licenceUrl
     * @param licenceKey
     */
    private void initLicense(String licenceUrl, String licenceKey) {
        WmLicense.initLicense(context, licenceUrl, licenceKey, new WmBaseListener() {
            @Override
            public void onLicenceLoaded(int code, String reason) {
                Log.e(TAG, "TXVideo onLicenceLoaded: code: " + code + ", reason: " + reason);
                isLicenceChecked = code == 0;
            }
        });
    }

}
