package com.example.video_player_flutter3;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.wmplayersdk.license.WmBaseListener;
import com.wmplayersdk.license.WmLicense;

import java.util.ArrayList;
import java.util.List;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import io.flutter.view.TextureRegistry;

/**
 * VideoPlayerFlutter3Plugin
 */
public class VideoPlayerFlutter3Plugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    private static final String TAG = "WmAliPlayerPlugin";
    private static final int MAX_RETRY_COUNT = 3;

    private MethodChannel channel;
    private TextureRegistry textureRegistry;
    private Context context;
    private FlutterPluginBinding binding;

    // License 状态枚举
    private enum LicenseState {
        NONE,           // 未初始化
        INITIALIZING,   // 初始化中
        SUCCESS,        // 初始化成功
        FAILED          // 初始化失败
    }

    // License 相关状态
    private LicenseState licenseState = LicenseState.NONE;
    private int retryCount = 0;
    private String currentLicenceUrl;
    private String currentLicenceKey;
    private List<Result> pendingLicenseResults = new ArrayList<>();

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        this.binding = flutterPluginBinding;
        textureRegistry = flutterPluginBinding.getTextureRegistry();
        context = flutterPluginBinding.getApplicationContext();
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "wm.io/videoPlayer/tencent");
        channel.setMethodCallHandler(this);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        String method = call.method;
        if ("setLicence".equals(method)) {
            String licenceUrl = call.argument("licenceUrl");
            String licenceKey = call.argument("licenceKey");
            setLicense(licenceUrl, licenceKey, result);
        } else if (method.equals("initialize")) {
            if (licenseState == LicenseState.SUCCESS) {
                final long textureId = create();
                result.success(textureId);
            } else {
                result.error("LICENSE_INIT_FAILED", "License initialization failed or not completed", -1);
            }
        } else {
            result.notImplemented();
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
    }

    /**
     * 创建播放器
     */
    private Long create() {
        TextureRegistry.SurfaceTextureEntry entry = textureRegistry.createSurfaceTexture();
        String methodName = "wm.io/videoPlayer/tencent" + entry.id();
        MethodChannel methodChannel = new MethodChannel(binding.getBinaryMessenger(), methodName);
//        VideoPlayer videoPlayer = new VideoPlayer(context, entry, methodChannel);
        new VideoPlayer(context, entry, methodChannel);
//        videoPlayers.put(entry.id(), videoPlayer);
        return entry.id();
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {

    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {

    }

    @Override
    public void onDetachedFromActivity() {

    }

    /**
     * 设置 License（支持重试和多次调用）
     *
     * @param licenceUrl
     * @param licenceKey
     * @param result
     */
    private void setLicense(String licenceUrl, String licenceKey, Result result) {
        // 如果已经成功初始化且参数相同，直接返回成功
        if (licenseState == LicenseState.SUCCESS &&
            licenceUrl.equals(currentLicenceUrl) &&
            licenceKey.equals(currentLicenceKey)) {
            result.success(null);
            return;
        }

        // 如果正在初始化中，将结果加入等待队列
        if (licenseState == LicenseState.INITIALIZING) {
            pendingLicenseResults.add(result);
            return;
        }

        // 开始新的初始化流程
        currentLicenceUrl = licenceUrl;
        currentLicenceKey = licenceKey;
        retryCount = 0;
        pendingLicenseResults.clear();
        pendingLicenseResults.add(result);

        initLicenseWithRetry();
    }

    /**
     * 带重试机制的 License 初始化
     */
    private void initLicenseWithRetry() {
        licenseState = LicenseState.INITIALIZING;
        Log.d(TAG, "Initializing license, attempt: " + (retryCount + 1));

        WmLicense.initLicense(context, currentLicenceUrl, currentLicenceKey, new WmBaseListener() {
            @Override
            public void onLicenceLoaded(int code, String reason) {
                Log.d(TAG, "License loaded: code=" + code + ", reason=" + reason + ", attempt=" + (retryCount + 1));

                if (code == 0) {
                    // 初始化成功
                    licenseState = LicenseState.SUCCESS;
                    notifyPendingResults(true, null);
                } else {
                    // 初始化失败，检查是否需要重试
                    retryCount++;
                    if (retryCount < MAX_RETRY_COUNT) {
                        Log.d(TAG, "License initialization failed, retrying... (" + retryCount + "/" + MAX_RETRY_COUNT + ")");
                        // 延迟重试
                        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                initLicenseWithRetry();
                            }
                        }, 1000); // 延迟1秒重试
                    } else {
                        // 重试次数用完，标记为失败
                        Log.e(TAG, "License initialization failed after " + MAX_RETRY_COUNT + " attempts");
                        licenseState = LicenseState.FAILED;
                        notifyPendingResults(false, "License initialization failed after " + MAX_RETRY_COUNT + " attempts: " + reason);
                    }
                }
            }
        });
    }

    /**
     * 通知所有等待的结果
     */
    private void notifyPendingResults(boolean success, String errorMessage) {
        for (Result result : pendingLicenseResults) {
            if (success) {
                result.success(null);
            } else {
                result.error("LICENSE_INIT_FAILED", errorMessage, -1);
            }
        }
        pendingLicenseResults.clear();
    }

}
