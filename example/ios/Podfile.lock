PODS:
  - Flutter (1.0.0)
  - integration_test (0.0.1):
    - Flutter
  - TXLiteAVSDK_Player (12.4.17856)
  - wm_ali_player (0.0.1):
    - Flutter
    - TXLiteAVSDK_Player (= 12.4.17856)

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - wm_ali_player (from `.symlinks/plugins/wm_ali_player/ios`)

SPEC REPOS:
  trunk:
    - TXLiteAVSDK_Player

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  wm_ali_player:
    :path: ".symlinks/plugins/wm_ali_player/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  TXLiteAVSDK_Player: 0c6a52db4ae7adb9d091da2bb572c3ab791903ba
  wm_ali_player: e441b7dff023f413c5784d2ee1ea1a573a6f9920

PODFILE CHECKSUM: 3bdfe558509c11b9b230b627423d5b4fa84e9f35

COCOAPODS: 1.16.2
