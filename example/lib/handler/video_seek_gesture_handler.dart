import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:video_player_flutter3_example/model/video_control_model.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';

class VideoSeekGestureHandler {
  final TXVideoPlayerController videoPlayerController;
  final ValueNotifier<ProgressInfo> progressInfoNotifier;
  final Function()? onDragStart;
  final Function()? onDragUpdate;
  final Function()? onDragEnd;
  final Function()? onDragCancel;

  double _dragStartProgress = 0.0;
  double _currentDragProgress = 0.0;

  VideoSeekGestureHandler({
    required this.videoPlayerController,
    required this.progressInfoNotifier,
    this.onDragStart,
    this.onDragUpdate,
    this.onDragEnd,
    this.onDragCancel,
  });

  GestureRecognizerFactoryWithHandlers<HorizontalDragGestureRecognizer> createHorizontalDragGestureRecognizer(
    BoxConstraints constraints,
  ) {
    return GestureRecognizerFactoryWithHandlers<HorizontalDragGestureRecognizer>(
      () => HorizontalDragGestureRecognizer()..gestureSettings = const DeviceGestureSettings(touchSlop: 0),
      (HorizontalDragGestureRecognizer instance) {
        instance
          ..onStart = (details) {
            _handleDragStart();
            onDragStart?.call();
          }
          ..onUpdate = (details) {
            _handleDragUpdate(details, constraints);
            onDragUpdate?.call();
          }
          ..onEnd = (details) {
            _handleDragEnd();
            onDragEnd?.call();
          }
          ..onCancel = () {
            _handleDragCancel();
            onDragCancel?.call();
          };
      },
    );
  }

  GestureRecognizerFactoryWithHandlers<TapGestureRecognizer> createTapGestureRecognizer(final Function()? onTap) {
    return GestureRecognizerFactoryWithHandlers<TapGestureRecognizer>(
      () => TapGestureRecognizer(),
      (TapGestureRecognizer instance) {
        instance.onTap = () {
          // _handleTap();
          onTap?.call();
        };
      },
    );
  }

  void _handleTap() {
    if (videoPlayerController.value.isPlaying) {
      videoPlayerController.pause();
    } else {
      videoPlayerController.resume();
    }
  }

  void _handleDragStart() {
    if (!_canDragProgress) return;
    _dragStartProgress = _getCurrentProgress();
    _currentDragProgress = _dragStartProgress;
    progressInfoNotifier.value = progressInfoNotifier.value.copyWith(
      currentMs: videoPlayerController.value.position,
      totalMs: videoPlayerController.value.duration,
      isDragging: true,
      progressPercent: _dragStartProgress,
      showActionBar: true,
    );
  }

  void _handleDragUpdate(DragUpdateDetails details, BoxConstraints constraints) {
    if (!_canDragProgress) return;
    final dragDelta = details.delta.dx / constraints.maxWidth;
    _currentDragProgress = (_currentDragProgress + dragDelta).clamp(0.0, 1.0);
    // 计算当前拖动位置对应的时间（毫秒）并通知
    final currentMs = (_currentDragProgress * videoPlayerController.value.duration).toInt();
    progressInfoNotifier.value = progressInfoNotifier.value.copyWith(
      currentMs: currentMs,
      totalMs: videoPlayerController.value.duration,
      isDragging: true,
      progressPercent: _currentDragProgress,
    );
  }

  void _handleDragEnd() {
    if (!_canDragProgress) return;
    final int targetPosition = (_currentDragProgress * videoPlayerController.value.duration).toInt();

    videoPlayerController.seek(targetPosition);
    // 如果视频暂停了，则恢复播放
    if (videoPlayerController.value.isPause ?? false) {
      videoPlayerController.resume();
    }
    progressInfoNotifier.value = progressInfoNotifier.value.copyWith(
      currentMs: targetPosition,
      totalMs: videoPlayerController.value.duration,
      isDragging: false,
      progressPercent: _currentDragProgress,
      isSeekLocked: true,
    );
    Future.delayed(const Duration(milliseconds: 250), () {
      progressInfoNotifier.value = progressInfoNotifier.value.copyWith(
        isSeekLocked: false,
      );
    });
  }

  void _handleDragCancel() {
    if (!_canDragProgress) return;
    _currentDragProgress = _getCurrentProgress();
    progressInfoNotifier.value = progressInfoNotifier.value.copyWith(
      isDragging: false,
    );
  }

  double _getCurrentProgress() {
    if (!videoPlayerController.value.isInitialized) return 0.0;
    return videoPlayerController.value.position / videoPlayerController.value.duration;
  }

  bool get _canDragProgress {
    return videoPlayerController.value.isInitialized && videoPlayerController.value.duration > 0;
  }
}
