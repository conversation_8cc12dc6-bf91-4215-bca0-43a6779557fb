import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';

import 'package:flutter/services.dart';
import 'package:video_player_flutter3_example/video_player_page.dart';
import 'package:wm_ali_player/tx_video_player.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await TXVideoPlayerController.setLicence(
    licenceUrl: "https://license.vod2.myqcloud.com/license/v2/1301695313_1/v_cube.license",
    licenceKey: "bfba1c9f65176b8e14a5a117b0939aaf",
  );
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final TXVideoPlayerController _playerController = TXVideoPlayerController();
  final h264Url = "https://food-v.changqingjiankang.com/时蔬大拌菜.mp4";
  final h265Url =
      "https://cdn-cq-changqing.cqslim.com/community-resource/2024-08-27/ab8ba135-2161-4094-a9b3-6744789ca585-2-%E6%9C%89%E6%9C%BA%E8%94%AC%E8%8F%9C%E7%A9%B6%E7%AB%9F%E6%9C%89%E4%BB%80%E4%B9%88%E4%BB%B7%E5%80%BC_%E6%A8%AA%E7%89%88.mp4";

  String get videoUrl => h264Url;
  final String licenseUrl = "https://license.vod2.myqcloud.com/license/v2/1301695313_1/v_cube.license";
  final String licenseKey = "bfba1c9f65176b8e14a5a117b0939aaf";

  @override
  void initState() {
    super.initState();
    _playerController.initialize();
    _playerController.onPlayerStateChanged.listen((value) {
      // print("VideoControlUi onPlayerStateChanged: $value");
      if (value.isPrepared) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    print("main dispose");
    super.dispose();
    _playerController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sw = View.of(context).physicalSize.width / View.of(context).devicePixelRatio;
    final sh = View.of(context).physicalSize.height / View.of(context).devicePixelRatio;
    return MaterialApp(
      home: Scaffold(
        appBar: isPortrait
            ? AppBar(
                title: const Text('Plugin example app'),
              )
            : null,
        body: Container(
          alignment: Alignment.topCenter,
          width: sw,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AspectRatio(
                aspectRatio: isPortrait ? 16 / 9 : sw / sh,
                child: ColoredBox(
                  color: Colors.black,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      TXVideoPlayer(_playerController),
                      _buildItem(
                          text: isPortrait ? '全屏' : '退出全屏',
                          onTap: () {
                            if (isPortrait) {
                              _enterFullScreen();
                            } else {
                              _exitFullScreen();
                            }
                          }),
                    ],
                  ),
                ),
              ),
              if (isPortrait)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('播放进度: ${_playerController.value.position} / ${_playerController.value.bufferedDuration} / ${_playerController.value.duration}'),
                    Text('是否Loading: ${_playerController.value.isLoading}'),
                    Text('当前网速: ${_playerController.value.netSpeed}'),
                    Text('是否播放: ${_playerController.value.isPlaying}'),
                    Text('是否暂停: ${_playerController.value.isPause}'),
                    Text('是否播放完成: ${_playerController.value.isPlayEnd}'),
                    Text('isPrepared: ${_playerController.value.isPrepared}'),
                    Text('视频比例: ${_playerController.value.aspectRatio}'),
                  ],
                ),
              if (isPortrait)
                Wrap(
                  children: [
                    _buildItem(
                        text: '启动播放',
                        onTap: () async {
                          setState(() {});
                          if (_playerController.value.rate != 1.0) {
                            await _playerController.setRate(1.0);
                          }
                          // _playerController.setStartTime(18 * 1000);
                          _playerController.play(videoUrl);
                        }),
                    _buildItem(
                        text: '暂停',
                        onTap: () {
                          _playerController.pause();
                        }),
                    _buildItem(
                        text: '恢复播放',
                        onTap: () {
                          _playerController.resume();
                        }),
                    // _buildItem(
                    //     text: '跳转',
                    //     onTap: () {
                    //       Navigator.push(context, MaterialPageRoute(builder: (context) => const SecondVideoPage()));
                    //     }),
                    _buildItem(
                        text: '全屏',
                        onTap: () {
                          _enterFullScreen();
                        }),
                    _buildItem(
                        text: '退出全屏',
                        onTap: () {
                          _exitFullScreen();
                        }),
                    _buildItem(
                        text: 'Seek',
                        onTap: () {
                          _playerController.seek(Random().nextInt(_playerController.value.duration));
                        }),
                    _buildItem(
                        text: _playerController.value.isMute ? "取消静音" : "静音",
                        onTap: () {
                          _playerController.setMute(!_playerController.value.isMute);
                        }),
                    _buildItem(
                        text: "设置倍速",
                        onTap: () {
                          _playerController.setRate(1.5);
                        }),
                  ],
                ),
              if (isPortrait)
                Builder(
                  builder: (context) {
                    return _buildItem(
                      text: '跳转',
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (context) => const VideoPlayerPage()));
                      },
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  bool get isPortrait => MediaQuery.of(context).orientation == Orientation.portrait;

  /// 退出全屏
  void _exitFullScreen() {
    if (!isPortrait) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    }
  }

  /// 进入全屏
  void _enterFullScreen() {
    if (isPortrait) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
      if (Platform.isIOS) {
        SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeRight]);
      } else {
        SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]);
        setState(() {});
      }
    }
  }

  Widget _buildItem({required String text, required Function onTap}) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: ElevatedButton(
        onPressed: () => onTap(),
        child: Text(text),
      ),
    );
  }
}
