// 在 VideoProgressBar 中添加
class DragTimeInfo {
  final int currentMs; // 当前拖拽位置（毫秒）
  final int totalMs; // 视频总时长（毫秒）

  DragTimeInfo(this.currentMs, this.totalMs);
}

class ProgressInfo {
  final int currentMs; // 当前拖拽位置（毫秒）
  final int totalMs; // 视频总时长（毫秒）
  final bool isDragging; // 是否正在拖拽
  final double progressPercent; // 进度百分比, 0-1
  final bool isSeekLocked; // 是否锁定 seek 后的状态更新(该字段避免在 seek 后，进度条抖动)
  final bool showActionBar; // 是否显示底部操作栏

  ProgressInfo({
    this.currentMs = 0,
    this.totalMs = 0,
    this.isDragging = false,
    this.progressPercent = 0.0,
    this.isSeekLocked = false,
    this.showActionBar = true,
  });

  ProgressInfo.empty() : this();

  ProgressInfo copyWith({
    int? currentMs,
    int? totalMs,
    bool? isDragging,
    double? progressPercent,
    bool? isSeekLocked,
    bool? showActionBar,
    bool? isPause,
  }) {
    return ProgressInfo(
      currentMs: currentMs ?? this.currentMs,
      totalMs: totalMs ?? this.totalMs,
      isDragging: isDragging ?? this.isDragging,
      progressPercent: progressPercent ?? this.progressPercent,
      isSeekLocked: isSeekLocked ?? this.isSeekLocked,
      showActionBar: showActionBar ?? this.showActionBar,
    );
  }
}
