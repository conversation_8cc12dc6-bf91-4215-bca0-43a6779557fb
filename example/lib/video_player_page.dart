import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player_flutter3_example/handler/video_seek_gesture_handler.dart';
import 'package:video_player_flutter3_example/model/video_control_model.dart';
import 'package:video_player_flutter3_example/util/video_util.dart';
import 'package:video_player_flutter3_example/widget/bottom_progress_and_action_widget.dart';
import 'package:video_player_flutter3_example/widget/full_screen_widget.dart';
import 'package:video_player_flutter3_example/widget/play_widget.dart';
import 'package:video_player_flutter3_example/widget/seek_position_widget.dart';
import 'package:video_player_flutter3_example/widget/top_action_widget.dart';
import 'package:wm_ali_player/tx_video_player.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';

class VideoPlayerPage extends StatefulWidget {
  const VideoPlayerPage({super.key});

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> with WidgetsBindingObserver {
  final TXVideoPlayerController _playerController = TXVideoPlayerController();
  late final ValueNotifier<ProgressInfo> _progressInfoNotifier = ValueNotifier(ProgressInfo.empty());
  final h264Url = "https://food-v.changqingjiankang.com/时蔬大拌菜.mp4";
  final h265Url =
      "https://cdn-cq-changqing.cqslim.com/community-resource/2024-08-27/ab8ba135-2161-4094-a9b3-6744789ca585-2-%E6%9C%89%E6%9C%BA%E8%94%AC%E8%8F%9C%E7%A9%B6%E7%AB%9F%E6%9C%89%E4%BB%80%E4%B9%88%E4%BB%B7%E5%80%BC_%E6%A8%AA%E7%89%88.mp4";

  String get videoUrl => h264Url;
  final String licenceUrl = "https://license.vod2.myqcloud.com/license/v2/1301695313_1/v_cube.license";
  final String licenceKey = "bfba1c9f65176b8e14a5a117b0939aaf";
  late final VideoSeekGestureHandler _videoSeekGestureHandler = VideoSeekGestureHandler(
    videoPlayerController: _playerController,
    progressInfoNotifier: _progressInfoNotifier,
    onDragEnd: () {
      if (!isPortrait) {
        _startHideTimer();
      }
    },
  );

  // 添加自动隐藏控制栏的定时器
  Timer? _hideControlsTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _playerController.initialize().then((onValue) {
      _playerController.setLoop(true);
      _playerController.play(videoUrl).then((onValue) {
        setState(() {});
      });
    });
    // 监听进度信息变化，处理拖动状态
    _progressInfoNotifier.addListener(_handleProgressInfoChange);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // 取消定时器
    _cancelHideTimer();
    _progressInfoNotifier.removeListener(_handleProgressInfoChange);
    super.dispose();
    _playerController.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _playerController.pause();
      _showControls();
    }
  }

  // 处理进度信息变化
  void _handleProgressInfoChange() {
    // 如果正在拖动，取消定时器
    if (_progressInfoNotifier.value.isDragging) {
      _cancelHideTimer();
    }
  }

  // 启动自动隐藏定时器
  void _startHideTimer() {
    // 先取消已有定时器
    _cancelHideTimer();

    // 创建新定时器
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      final isPause = _playerController.value.isPause ?? false;
      final showActionBar = _progressInfoNotifier.value.showActionBar;
      if (mounted && showActionBar && !isPortrait && !isPause) {
        _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
          showActionBar: false,
        );
      }
    });
  }

  // 切换控制栏显示状态
  void _toggleControlsVisibility() {
    final newShowActionBar = !_progressInfoNotifier.value.showActionBar;

    _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
      showActionBar: newShowActionBar,
    );

    if (newShowActionBar) {
      _startHideTimer();
    } else {
      _cancelHideTimer();
    }
  }

  // 取消自动隐藏定时器
  void _cancelHideTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = null;
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.black, // 状态栏背景色
        statusBarIconBrightness: Brightness.light, // 状态栏图标颜色

        systemNavigationBarColor: Colors.black,
        systemNavigationBarIconBrightness: Brightness.light, // 图标亮色
      ),
    );
    return Scaffold(
      // appBar: PreferredSize(
      //   preferredSize: Size(100, 0),
      //   child: AppBar(
      //     title: const Text('Video Player'),
      //   ),
      // ),
      body: PopScope(
        // 返回键
        canPop: isPortrait,
        onPopInvokedWithResult: (didPop, result) {
          if (isPortrait && Navigator.canPop(context)) {
            Navigator.pop(context);
          } else {
            exitFullScreen();
          }
        },
        child: OrientationBuilder(
          // 方向改变
          builder: (context, orientation) {
            if (orientation == Orientation.portrait) {
              _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
                showActionBar: true,
              );
            } else {
              _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
                showActionBar: _playerController.value.isPause ?? false,
              );
            }
            return RawGestureDetector(
              gestures: {
                HorizontalDragGestureRecognizer:
                    _videoSeekGestureHandler.createHorizontalDragGestureRecognizer(BoxConstraints(maxWidth: MediaQuery.of(context).size.width)),
                TapGestureRecognizer: _videoSeekGestureHandler.createTapGestureRecognizer(_handleBodyTap),
              },
              child: Container(
                color: Colors.black,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 视频区域
                        _buildVideoArea(),
                        // 全屏按钮
                        FullScreenWidget(
                          playerController: _playerController,
                          onFullScreenCallback: () {
                            _hideControls();
                          },
                        ),
                      ],
                    ),
                    // 拖拽进度条时，显示当前拖拽位置的进度
                    _buildSeekPosition(),
                    // 底部操作栏
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: BottomProgressAndActionWidget(
                        playerController: _playerController,
                        progressInfoNotifier: _progressInfoNotifier,
                        onManualPauseCallback: (isPause) {
                          if (isPause) {
                            _cancelHideTimer();
                          } else {
                            _startHideTimer();
                          }
                        },
                      ),
                    ),
                    Positioned(
                      top: MediaQuery.of(context).padding.top,
                      child: TopActionWidget(progressInfoNotifier: _progressInfoNotifier),
                    ),
                    // 顶部操作栏
                    // Positioned(
                    //   top: 100,
                    //   left: 0,
                    //   child: TopActionWidget(progressInfoNotifier: _progressInfoNotifier),
                    // ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 拖拽进度条时，显示当前拖拽位置的进度
  Widget _buildSeekPosition() {
    return Positioned(
      bottom: 120,
      child: SeekPositionWidget(
        progressInfoNotifier: _progressInfoNotifier,
      ),
    );
  }

  /// 视频区域
  Widget _buildVideoArea() {
    final sw = View.of(context).physicalSize.width / View.of(context).devicePixelRatio;
    final sh = View.of(context).physicalSize.height / View.of(context).devicePixelRatio;
    // return AspectRatio(
    //   // aspectRatio: isPortrait ? 16 / 9 : sw / sh,
    //   aspectRatio: 720 / 1280,
    //   child: Stack(
    //     alignment: Alignment.center,
    //     clipBehavior: Clip.none,
    //     children: [
    //       TXVideoPlayer(_playerController),
    //       PlayWidget(
    //           playerController: _playerController,
    //           onPlayCallback: () {
    //             if (!isPortrait) {
    //               _startHideTimer();
    //             }
    //           }),
    //     ],
    //   ),
    // );
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        TXVideoPlayer(_playerController),
        PlayWidget(
            playerController: _playerController,
            onPlayCallback: () {
              if (!isPortrait) {
                _startHideTimer();
              }
            }),
      ],
    );
  }

  /// 1. 竖屏模式下，点击暂停或者播放
  /// 2. 横屏模式下，点击会出现控制栏，同时视频中间会出现一个暂停按钮
  ///  2.1 横屏模式下再次点击，控制栏消失，视频中间的暂停按钮消失
  void _handleBodyTap() {
    if (isPortrait) {
      if (_playerController.value.isPlaying) {
        _playerController.pause();
      } else {
        _playerController.resume();
      }
    } else {
      _toggleControlsVisibility();
    }
  }

  /// 隐藏控制栏
  void _hideControls() {
    _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
      showActionBar: false,
    );
  }

  /// 显示控制栏
  void _showControls() {
    _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
      showActionBar: true,
    );
  }

  bool get isPortrait => MediaQuery.of(context).orientation == Orientation.portrait;
}
