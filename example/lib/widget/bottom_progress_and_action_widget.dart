import 'package:flutter/material.dart';
import 'package:video_player_flutter3_example/model/video_control_model.dart';
import 'package:video_player_flutter3_example/widget/land_spance_action_bar.dart';
import 'package:video_player_flutter3_example/widget/video_progress_bar_v2.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';

/// 底部进度条和操作栏
class BottomProgressAndActionWidget extends StatefulWidget {
  final TXVideoPlayerController playerController;
  final ValueNotifier<ProgressInfo> progressInfoNotifier;
  // 手动暂停/恢复播放 回调
  final Function(bool isPause)? onManualPauseCallback;
  const BottomProgressAndActionWidget({
    super.key,
    required this.playerController,
    required this.progressInfoNotifier,
    this.onManualPauseCallback,
  });

  @override
  State<BottomProgressAndActionWidget> createState() => _BottomProgressAndActionWidgetState();
}

class _BottomProgressAndActionWidgetState extends State<BottomProgressAndActionWidget> {
  TXVideoPlayerController get _playerController => widget.playerController;
  ValueNotifier<ProgressInfo> get _progressInfoNotifier => widget.progressInfoNotifier;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: _progressInfoNotifier,
        builder: (context, value, _) {
          return AnimatedPositioned(
            duration: const Duration(milliseconds: 350),
            curve: Curves.easeInOut,
            left: 0,
            right: 0,
            bottom: value.showActionBar ? 0 : -100,
            child: Container(
              padding: const EdgeInsets.only(bottom: 10, left: 10, right: 10),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.5),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Column(
                children: [
                  VideoProgressBarV2(
                    playerController: _playerController,
                    progressInfoNotifier: _progressInfoNotifier,
                  ),
                  if (MediaQuery.of(context).orientation == Orientation.landscape)
                    LandSpaceActionBar(
                      playerController: _playerController,
                      progressInfoNotifier: _progressInfoNotifier,
                      onManualPauseCallback: widget.onManualPauseCallback,
                    ),
                ],
              ),
            ),
          );
        });
  }
}
