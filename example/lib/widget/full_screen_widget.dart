import 'package:flutter/material.dart';
import 'package:video_player_flutter3_example/model/video_control_model.dart';
import 'package:video_player_flutter3_example/util/video_util.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';
import 'package:wm_ali_player/tx_video_player_value.dart';

class FullScreenWidget extends StatefulWidget {
  final TXVideoPlayerController playerController;
  final Function()? onFullScreenCallback;

  const FullScreenWidget({
    super.key,
    required this.playerController,
    this.onFullScreenCallback,
  });

  @override
  State<FullScreenWidget> createState() => _FullScreenWidgetState();
}

class _FullScreenWidgetState extends State<FullScreenWidget> {
  TXVideoPlayerController get _playerController => widget.playerController;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<TXVideoPlayerValue>(
      stream: _playerController.onPlayerStateChanged,
      builder: (context, snapshot) {
        if (!isPortrait) return const SizedBox.shrink();
        if (snapshot.data == null) return const SizedBox.shrink();
        if (!snapshot.data!.isInitialized) return const SizedBox.shrink();
        if (!snapshot.data!.isPrepared) return const SizedBox.shrink();
        if (snapshot.data!.aspectRatio < 1.0) return const SizedBox.shrink();
        return Padding(
          padding: const EdgeInsets.only(top: 16),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(20),
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                enterFullScreen();
                widget.onFullScreenCallback?.call();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(50),
                  border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.fullscreen, color: Color(0xff999999), size: 20),
                    SizedBox(width: 4),
                    Text(
                      "全屏观看",
                      style: TextStyle(color: Color(0xff999999), fontSize: 13),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  bool get isPortrait => MediaQuery.of(context).orientation == Orientation.portrait;
}
