import 'package:flutter/material.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';
import 'package:wm_ali_player/tx_video_player_value.dart';

/// 播放按钮:展示在视频播放器中间
class PlayWidget extends StatefulWidget {
  final TXVideoPlayerController playerController;
  final Function()? onPlayCallback;
  const PlayWidget({super.key, required this.playerController, this.onPlayCallback});

  @override
  State<PlayWidget> createState() => _PlayWidgetState();
}

class _PlayWidgetState extends State<PlayWidget> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<TXVideoPlayerValue>(
      stream: widget.playerController.onPlayerStateChanged,
      builder: (context, snapshot) {
        if (snapshot.data == null) return const SizedBox.shrink();
        if (!snapshot.data!.isInitialized) return const SizedBox.shrink();
        if (!snapshot.data!.isPrepared) return const SizedBox.shrink();
        return snapshot.data!.isPlaying
            ? const SizedBox.shrink()
            : TweenAnimationBuilder<double>(
                tween: Tween(begin: 2.0, end: 1.0),
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeOut,
                builder: (context, scale, child) {
                  return Transform.scale(
                    scale: scale,
                    child: IconButton(
                      onPressed: () {
                        widget.playerController.resume();
                        widget.onPlayCallback?.call();
                      },
                      icon: Icon(
                        Icons.play_arrow_rounded,
                        color: Colors.white.withValues(alpha: 0.8),
                        size: 60,
                      ),
                    ),
                  );
                },
              );
      },
    );
  }
}
