import 'dart:io';

import 'package:flutter/material.dart';
import 'package:video_player_flutter3_example/model/video_control_model.dart';
import 'package:video_player_flutter3_example/util/video_util.dart';

/// 拖拽进度条时，显示当前拖拽位置的进度
class SeekPositionWidget extends StatelessWidget {
  final ValueNotifier<ProgressInfo> progressInfoNotifier;

  const SeekPositionWidget({
    super.key,
    required this.progressInfoNotifier,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<ProgressInfo>(
      valueListenable: progressInfoNotifier,
      builder: (context, progressInfo, child) {
        final portrait = MediaQuery.of(context).orientation == Orientation.portrait;
        if (!progressInfo.isDragging) return const SizedBox.shrink();
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: portrait ? Colors.white.withValues(alpha: 0.1) : Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              Text(
                formatMsToMMSS(progressInfo.currentMs),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              Text(
                ' / ${formatMsToMMSS(progressInfo.totalMs)}',
                style: TextStyle(
                  color: portrait ? Color(0xff949392) : Colors.grey,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
