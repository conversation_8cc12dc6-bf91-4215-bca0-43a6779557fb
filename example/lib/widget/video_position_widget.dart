import 'package:flutter/material.dart';
import 'package:video_player_flutter3_example/model/video_control_model.dart';
import 'package:video_player_flutter3_example/util/video_util.dart';

class VideoPositionWidget extends StatelessWidget {
  final ValueNotifier<DragTimeInfo?> playPositionNotifier;
  const VideoPositionWidget({super.key, required this.playPositionNotifier});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<DragTimeInfo?>(
      valueListenable: playPositionNotifier,
      builder: (context, dragTimeInfo, child) {
        if (dragTimeInfo == null) return const SizedBox.shrink();

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          child: Text(
            '${formatMsToMMSS(dragTimeInfo.currentMs)} / ${formatMsToMMSS(dragTimeInfo.totalMs)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        );
      },
    );
  }
}
