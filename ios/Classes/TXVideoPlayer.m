//
//  TXVideoPlayer.m
//  wm_ali_player
//
//  Created by 杨超 on 2022/2/11.
//

#import "TXVideoPlayer.h"
#import <TXLiteAVSDK_Player/TXLiteAVSDK.h>
#import <libkern/OSAtomic.h>


@interface TXVideoPlayer ()<TXVodPlayListener,TXVideoCustomProcessDelegate>

@property (nonatomic, strong) TXVodPlayer *txVodPlayer;

@property (nonatomic, strong) FlutterMethodChannel *methodChannel;

@property (nonatomic, assign) int64_t textureId;


@end

@implementation TXVideoPlayer{
    // 最新的一帧
    CVPixelBufferRef volatile _latestPixelBuffer;
    // 旧的一帧
    CVPixelBufferRef _lastBuffer;
    
    id<FlutterTextureRegistry> _textureRegistry;
}

-(instancetype)init{
    self = [super init];
    if (self) {
        _latestPixelBuffer = nil;
        _lastBuffer = nil;
        _textureId = -1;
    }
    return self;
}

-(void)bindRelation:(FlutterMethodChannel *)channel textureId:(int64_t)textureId textureRegistry:(id<FlutterTextureRegistry>)textureRegistry{
    _textureRegistry = textureRegistry;
    self.textureId = textureId;
    self.txVodPlayer = [TXVodPlayer new];
    self.txVodPlayer.vodDelegate = self;
    [self.txVodPlayer setVideoProcessDelegate:self];
    self.txVodPlayer.enableHWAcceleration = YES;

    [self initMethodCallHandler:channel];
}

- (CVPixelBufferRef _Nullable)copyPixelBuffer {
    CVPixelBufferRef pixelBuffer = _latestPixelBuffer;
    while (!OSAtomicCompareAndSwapPtrBarrier(pixelBuffer, nil,
                                             (void **)&_latestPixelBuffer)) {
        pixelBuffer = _latestPixelBuffer;
    }
    
    //CVPixelBufferRef pixelBuffer2 = [_glRender copyPixelBuffer];
    return pixelBuffer;
}

#pragma mark - TXVodPlayListener

/**
 * 点播事件通知
 *
 * @param player 点播对象
 * @param EvtID 参见TXLiveSDKTypeDef.h
 * @param param 参见TXLiveSDKTypeDef.h
 * @see TXVodPlayer
 */
- (void)onPlayEvent:(TXVodPlayer *)player event:(int)EvtID withParam:(NSDictionary*)param
{
    if (EvtID == PLAY_EVT_PLAY_PROGRESS && player.isPlaying) {
        int bufferedS = [param[EVT_PLAYABLE_DURATION] intValue];
        int progressS = [param[EVT_PLAY_PROGRESS] intValue];
        int totalS = [param[EVT_PLAY_DURATION] intValue];
        NSMutableDictionary *data = [[NSMutableDictionary alloc]init];
        [data setValue:@(bufferedS * 1000) forKey:@"buffered"];
        [data setValue:@(progressS * 1000) forKey:@"position"];
        [data setValue:@(totalS * 1000) forKey:@"duration"];
        [self.methodChannel invokeMethod:@"onPlayProgress" arguments:data];
    } else if (EvtID == PLAY_EVT_PLAY_BEGIN) {
        [self.methodChannel invokeMethod:@"onPlayBegin" arguments:nil];
    } else if (EvtID == PLAY_EVT_PLAY_END) {
        [self.methodChannel invokeMethod:@"onPlayEnd" arguments:nil];
        [self.txVodPlayer stopPlay];
    } else if (EvtID == PLAY_EVT_PLAY_LOADING) {
        [self.methodChannel invokeMethod:@"onLoading" arguments:nil];
    } else if (EvtID == PLAY_EVT_VOD_LOADING_END) {
        [self.methodChannel invokeMethod:@"onLoadingEnd" arguments:nil];
    } else if (EvtID == PLAY_ERR_NET_DISCONNECT) {
        [self.methodChannel invokeMethod:@"onNetDisconnect" arguments:nil];
    } else if (EvtID == PLAY_EVT_VOD_PLAY_PREPARED) {
        NSMutableDictionary *data = [[NSMutableDictionary alloc]init];
        [data setValue:@(player.width) forKey:@"width"];
        [data setValue:@(player.height) forKey:@"height"];
        [self.methodChannel invokeMethod:@"onPrepared" arguments:data];
    } else if (EvtID == PLAY_EVT_RCV_FIRST_I_FRAME) {
        [self.methodChannel invokeMethod:@"onFirstFrame" arguments:nil];
    }
}

/**
 * 网络状态通知
 *
 * @param player 点播对象
 * @param param 参见TXLiveSDKTypeDef.h
 * @see TXVodPlayer
 */
- (void)onNetStatus:(TXVodPlayer *)player withParam:(NSDictionary*)param
{
    NSMutableDictionary *data = [[NSMutableDictionary alloc]init];
    [data setValue:[param objectForKey:NET_STATUS_NET_SPEED] forKey:@"netSpeed"];
    [self.methodChannel invokeMethod:@"onNetStatus" arguments:data];
}

/**
 * 视频渲染对象回调
 * @param pixelBuffer   渲染图像
 * @return              返回YES则SDK不再显示；返回NO则SDK渲染模块继续渲染
 *  说明：渲染图像的数据类型为config中设置的renderPixelFormatType
 */
- (BOOL)onPlayerPixelBuffer:(CVPixelBufferRef)pixelBuffer
{
    if (_lastBuffer == nil) {
        _lastBuffer = CVPixelBufferRetain(pixelBuffer);
        CFRetain(pixelBuffer);
    } else if (_lastBuffer != pixelBuffer) {
        CVPixelBufferRelease(_lastBuffer);
        _lastBuffer = CVPixelBufferRetain(pixelBuffer);
        CFRetain(pixelBuffer);
    }
    
    CVPixelBufferRef newBuffer = pixelBuffer;
    
    CVPixelBufferRef old = _latestPixelBuffer;
    while (!OSAtomicCompareAndSwapPtrBarrier(old, newBuffer,
                                             (void **)&_latestPixelBuffer)) {
        old = _latestPixelBuffer;
    }
    
    if (old && old != pixelBuffer) {
        CFRelease(old);
    }
    if (_textureId >= 0) {
        [_textureRegistry textureFrameAvailable:_textureId];
    }
    return NO;
}

- (void)initMethodCallHandler:(FlutterMethodChannel *)methodChannel{
    _methodChannel = methodChannel;
    __weak __typeof__(self) weakSelf = self;
    [_methodChannel setMethodCallHandler:^(FlutterMethodCall * _Nonnull call, FlutterResult  _Nonnull result) {
        NSObject* obj = [call arguments];
        if ([obj isKindOfClass:NSDictionary.class]) {
            NSDictionary *dic = (NSDictionary*)obj;
            NSObject *arguments= [dic objectForKey:@"arg"];
            [weakSelf onMethodCall:call result:result arg:arguments?:@""];
        }else{
            [weakSelf onMethodCall:call result:result arg:@""];
        }
    }];
}

- (void)onMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result arg:(NSObject*)arg{
    NSString* method = [call method];
    NSLog(@"ios.onMethodCall.name:%@",method);
    SEL methodSel=NSSelectorFromString([NSString stringWithFormat:@"%@:",method]);
    NSArray *arr = @[call,result,arg];
    if([self respondsToSelector:methodSel]){
        IMP imp = [self methodForSelector:methodSel];
        void (*func)(id, SEL, NSArray*) = (void *)imp;
        func(self, methodSel, arr);
    }else{
        result(FlutterMethodNotImplemented);
    }
}



- (void)setConfig:(NSArray *)arr{
    FlutterResult result = arr[1];
    NSDictionary *config = arr[2];
    if (_txVodPlayer != nil) {
        TXVodPlayConfig *playerConfig = [[TXVodPlayConfig alloc]init];
        playerConfig.headers = config;
        _txVodPlayer.config = playerConfig;
    }
    result(nil);
}
- (void)play:(NSArray *)arr{
    FlutterResult result = arr[1];
    NSString* url = arr[2];
    if (_txVodPlayer != nil) {
        [_txVodPlayer startVodPlay:url];
    }
    result(nil);
}
- (void)setStartTime:(NSArray *)arr{
    FlutterResult result = arr[1];
    CGFloat time = [arr[2] floatValue];
    if (_txVodPlayer != nil) {
        [_txVodPlayer setStartTime:time / 1000];
    }
    result(nil);
}
- (void)pause:(NSArray *)arr{
    FlutterResult result = arr[1];
    if (_txVodPlayer != nil) {
        [_txVodPlayer pause];
    }
    result(nil);
}

- (void)dispose:(NSArray *)arr{
    FlutterResult result = arr[1];
    if (_txVodPlayer != nil) {
        [_txVodPlayer stopPlay];
    }
    CVPixelBufferRef old = _latestPixelBuffer;
    while (!OSAtomicCompareAndSwapPtrBarrier(old, nil,
                                             (void **)&_latestPixelBuffer)) {
        old = _latestPixelBuffer;
    }
    if (old) {
        CFRelease(old);
    }

    if (_textureId >= 0) {
        [_textureRegistry unregisterTexture:_textureId];
        _textureId = -1;
        _textureRegistry = nil;
    }

    if (_lastBuffer) {
        CVPixelBufferRelease(_lastBuffer);
        _lastBuffer = nil;
    }
    result(nil);
}

- (void)resume:(NSArray *)arr{
    FlutterResult result = arr[1];
    if (_txVodPlayer != nil) {
        [_txVodPlayer resume];
    }
    result(nil);
}
- (void)setAutoPlay:(NSArray *)arr{
    FlutterResult result = arr[1];
    BOOL b = [arr[2] boolValue];
    if (_txVodPlayer != nil) {
        _txVodPlayer.isAutoPlay = b;
    }
    result(nil);
}
- (void)seek:(NSArray *)arr{
    FlutterResult result = arr[1];
#warning 这块有不一样的，dart目前定义的是int类型
    float progress = [arr[2] intValue] / 1000.0;
    if (_txVodPlayer != nil) {
        [_txVodPlayer seek:progress];
    }
    result(nil);
}
- (void)setLoop:(NSArray *)arr{
    FlutterResult result = arr[1];
    BOOL b = [arr[2] boolValue];
    if (_txVodPlayer != nil) {
        _txVodPlayer.loop = b;
    }
    result(nil);
}
- (void)setMute:(NSArray *)arr{
    FlutterResult result = arr[1];
    BOOL bEnable = [arr[2] boolValue];
    if (_txVodPlayer != nil) {
        [_txVodPlayer setMute:bEnable];
    }
    result(nil);
}
- (void)setRate:(NSArray *)arr{
    FlutterResult result = arr[1];
#warning 这块有不一样的，dart目前定义的是double类型
    float rate = [arr[2] floatValue];
    if (_txVodPlayer != nil) {
        [_txVodPlayer setRate:rate];
    }
    result(nil);
}

- (void)invokeMethod:(NSString *)method arguments:(NSDictionary *)arguments{
    [self.methodChannel invokeMethod:method arguments:arguments];
}

-(void)dealloc{
    NSLog(@"TXVideoPlayer--释放");
}
@end
