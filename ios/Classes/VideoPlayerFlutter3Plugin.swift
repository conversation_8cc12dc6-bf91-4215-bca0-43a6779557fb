import Flutter
import UIKit

public class VideoPlayerFlutter3Plugin: NSObject, FlutterPlugin {
  public static func register(with registrar: FlutterPluginRegistrar) {
  WmAliPlayerPlugin.register(with: registrar);
//     let channel = FlutterMethodChannel(name: "video_player_flutter3", binaryMessenger: registrar.messenger())
//     let instance = VideoPlayerFlutter3Plugin()
//     registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    switch call.method {
    case "getPlatformVersion":
      result("iOS " + UIDevice.current.systemVersion)
    default:
      result(FlutterMethodNotImplemented)
    }
  }
}
