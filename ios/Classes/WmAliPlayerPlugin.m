#import "WmAliPlayerPlugin.h"
#import "TXVideoPlayer.h"
#import <TXLiteAVSDK_Player/TXLiteAVSDK.h>

@interface WmAliPlayerPlugin ()<TXLiveBaseDelegate>

@property(readonly, weak, nonatomic) NSObject<FlutterTextureRegistry> *registry;
@property(readonly, strong, nonatomic) NSObject<FlutterPluginRegistrar> *registrar;
@property(readonly, weak, nonatomic) NSObject<FlutterBinaryMessenger> *messenger;
@end


@implementation WmAliPlayerPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
    FlutterMethodChannel* channel = [FlutterMethodChannel
                                     methodChannelWithName:@"wm.io/videoPlayer/tencent"
                                     binaryMessenger:[registrar messenger]];
    WmAliPlayerPlugin* instance = [[WmAliPlayerPlugin alloc] initWithRegistrar:registrar];
    [registrar addMethodCallDelegate:instance channel:channel];
}

- (instancetype)initWithRegistrar:(NSObject<FlutterPluginRegistrar> *)registrar {
    self = [super init];
    NSAssert(self, @"super init cannot be nil");
    _registrar = registrar;
    _registry = [registrar textures];
    _messenger = [registrar messenger];
    return self;
}

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    if ([@"getPlatformVersion" isEqualToString:call.method]) {
        result([@"iOS " stringByAppendingString:[[UIDevice currentDevice] systemVersion]]);
    } else if ([@"initialize" isEqualToString:call.method]) {
        result(@([self create]));
    } else if ([@"setLicence" isEqualToString:call.method]) {
        [self setLicenceOnMethodCall:call result:result];
    } else {
        result(FlutterMethodNotImplemented);
    }
}


- (void)setLicenceOnMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    NSString *licenceUrl = [NSString stringWithFormat:@"%@", [call arguments][@"licenceUrl"]];
    NSString *licenceKey = [NSString stringWithFormat:@"%@", [call arguments][@"licenceKey"]];
    [TXLiveBase sharedInstance].delegate = self;
    [TXLiveBase setLicenceURL:licenceUrl key:licenceKey];
}

-(void)onLicenceLoaded:(int)result Reason:(NSString *)reason {
    NSLog(@"[wm_ali_player] - [onLicenceLoaded] result=%d reason=%@", result, reason);
}

- (int64_t)create{
    TXVideoPlayer *player = [[TXVideoPlayer alloc]init];
    int64_t textureId = [self.registry registerTexture:player];
    NSString *methodName = [NSString stringWithFormat:@"wm.io/videoPlayer/tencent%lld",textureId];
    FlutterMethodChannel *channel = [FlutterMethodChannel methodChannelWithName:methodName binaryMessenger:self.messenger];
    [player bindRelation:channel textureId:textureId textureRegistry:self.registry];
    return  textureId;
}

@end
