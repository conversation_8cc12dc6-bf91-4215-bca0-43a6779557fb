import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';
import 'package:wm_ali_player/tx_video_player_value.dart';

// author: Liu<PERSON>long
// date: 2025/3/12
// desc:

class TXVideoPlayer extends StatefulWidget {
  final TXVideoPlayerController playerController;

  const TXVideoPlayer(this.playerController, {super.key});

  @override
  State createState() => _TXVideoPlayerState();
}

class _TXVideoPlayerState extends State<TXVideoPlayer> {
  TXVideoPlayerController get _txCtrl => widget.playerController;

  TXVideoPlayerValue get _value => _txCtrl.value;

  StreamSubscription<TXVideoPlayerValue>? _playerStateSubscription;

  bool _isPrepared = false;

  @override
  void initState() {
    super.initState();
    _playerStateSubscription = _txCtrl.onPlayerStateChanged.listen((value) {
      if (value.isInitialized && value.isPrepared && !_isPrepared) {
        setState(() {});
        _isPrepared = true;
      }
    });
  }

  @override
  void dispose() {
    _playerStateSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_txCtrl.textureId == TXVideoPlayerController.kUninitializedTextureId) {
      return const Center(
        child: CupertinoActivityIndicator(),
      );
    }
    if (!_txCtrl.value.isInitialized || !_txCtrl.value.isPrepared) {
      return const Center(
        child: CupertinoActivityIndicator(),
      );
    }
    final (boxWidth, boxHeight) = boxSize;
    return Center(
      child: SizedBox(
        width: boxWidth,
        height: boxHeight,
        child: AspectRatio(
          aspectRatio: _value.aspectRatio,
          child: Texture(
            textureId: _txCtrl.textureId,
            filterQuality: FilterQuality.low,
          ),
        ),
      ),
    );
  }

  double get aspectRatio {
    final sw = View.of(context).physicalSize.width / View.of(context).devicePixelRatio;
    final sh = View.of(context).physicalSize.height / View.of(context).devicePixelRatio;
    // 横屏视频
    if (_value.aspectRatio >= 1.0) {
      return isPortrait ? _value.aspectRatio : sw / sh;
    } else {
      // 竖屏视频
      return _value.aspectRatio;
    }
  }

  /// 获取屏幕宽高
  (double, double) get boxSize {
    // 屏幕宽
    final sw = View.of(context).physicalSize.width / View.of(context).devicePixelRatio;
    // 屏幕高
    final sh = View.of(context).physicalSize.height / View.of(context).devicePixelRatio;
    // 视频比例
    final aspectRatio = _value.aspectRatio;
    // 竖屏
    if (isPortrait) {
      return (sw, sw / aspectRatio);
      // return (sw, sh);
    } else {
      return (sh * aspectRatio, sh);
    }
    if (aspectRatio >= 1.0) {
      if (isPortrait) {
        return (sw, sw / aspectRatio);
      } else {
        return (sh * aspectRatio, sh);
      }
    } else {
      // 竖屏视频
      if (isPortrait) {
        return (sw, sw / aspectRatio);
      } else {
        return (sh * aspectRatio, sh);
      }
    }
  }

  bool get isPortrait => MediaQuery.of(context).orientation == Orientation.portrait;
}
