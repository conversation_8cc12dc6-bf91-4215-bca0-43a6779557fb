import 'dart:async';

import 'package:flutter/services.dart';
import 'package:wm_ali_player/tx_video_player_value.dart';

// author: Liusilong
// date: 2025/3/12
// desc:

class TXVideoPlayerController {
  static final MethodChannel _nativeChannel = const MethodChannel('wm.io/videoPlayer/tencent');

  MethodChannel? _methodChannel;
  static const int kUninitializedTextureId = -1;
  int _textureId = kUninitializedTextureId;

  int get textureId => _textureId;

  /// 播放器相关属性
  TXVideoPlayerValue _value = TXVideoPlayerValue.uninitialized();

  TXVideoPlayerValue get value => _value;

  /// 播放监听
  VoidCallback? _playListener;

  /// 播放器状态流
  final StreamController<TXVideoPlayerValue> _playerStateController = StreamController<TXVideoPlayerValue>.broadcast();

  /// 获取播放状态流
  Stream<TXVideoPlayerValue> get onPlayerStateChanged => _playerStateController.stream;

  /// 添加播放监听
  void addPlayListener(VoidCallback playListener) {
    _playListener = playListener;
  }

  /// 初始化播放器 Licence
  /// [licenceUrl] 播放器 license 地址
  /// [licenceKey] 播放器 license key
  static Future<void> setLicence({
    required String licenceUrl,
    required String licenceKey,
  }) async {
    _nativeChannel.invokeMethod('setLicence', {
      'licenceUrl': licenceUrl,
      'licenceKey': licenceKey,
    });
  }

  /// 初始化播放器
  /// [licenceUrl] 播放器 license 地址
  /// [licenceKey] 播放器 license key
  /// [config] 播放器配置，如 Referer
  Future<void> initialize({Map<String, String>? config}) async {
    _textureId = await _nativeChannel.invokeMethod('initialize') ?? kUninitializedTextureId;
    if (_textureId != kUninitializedTextureId) {
      _methodChannel = MethodChannel('wm.io/videoPlayer/tencent$_textureId');
      _methodChannel!.setMethodCallHandler(_methodHandler);
      // if (autoPlay) setAutoPlay(true);
      if (config != null) setConfig(config);
    }
  }

  /// 设置相关配置
  Future<void> setConfig(Map<String, String> map) async {
    await _methodChannel!.invokeMethod('setConfig', {'arg': map});
  }

  /// 启动播放
  /// [url] 视频地址，网络的或者本地的视频地址都可以
  Future<void> play(String url) async {
    _value = TXVideoPlayerValue.uninitialized();
    _value = _value.copyWith(url: url);
    await _methodChannel!.invokeMethod('play', {'arg': url});
    _notifyChanged();
  }

  /// 重播
  Future<void> replay() async {
    if (_value.url != null) {
      await play(_value.url!);
    }
  }

  /// 暂停播放
  Future<void> pause() async {
    if (_value.isPlaying) {
      await _methodChannel!.invokeMethod('pause');
      _value = _value.copyWith(isPlaying: false, isPause: true);
      _notifyChanged();
    }
  }

  /// 恢复播放
  Future<void> resume() async {
    await _methodChannel!.invokeMethod('resume');
    _value = _value.copyWith(isPlaying: true, isPause: false);
    _notifyChanged();
  }

  /// 设置播放器是否自动播放
  /// [autoPlay] 是否自动播放 true: 自动播放，false: 不自动播放
  Future<void> setAutoPlay(bool autoPlay) async {
    await _methodChannel!.invokeMethod('setAutoPlay', {'arg': autoPlay});
  }

  /// 跳转
  /// [time] 跳转到的时间，单位：毫秒
  /// 说明：如果当前播放未完成，seek 之后播放器会自动播放；如果当前播放已经完成，seek 之后还需调用 resume
  Future<void> seek(int time) async {
    await _methodChannel!.invokeMethod('seek', {'arg': time});
    if (_value.isPlayEnd) {
      await resume();
    }
  }

  /// 设置播放器循环播放
  /// [isLoop] true： 循环播放；false：不循环播放。默认不循环
  Future<void> setLoop(bool isLoop) async {
    await _methodChannel!.invokeMethod('setLoop', {'arg': isLoop ?? false});
    _value = _value.copyWith(isLoop: isLoop);
    _notifyChanged();
  }

  /// 设置播放器是否静音
  /// [mute] true： 静音；false：非静音
  Future<void> setMute(bool mute) async {
    await _methodChannel!.invokeMethod('setMute', {'arg': mute ?? false});
    _value = _value.copyWith(isMute: mute);
    _notifyChanged();
  }

  /// 倍速播放
  /// [rate] 倍速：0.5，1.0，1.25，1.5，2.0 等
  Future<void> setRate(double rate) async {
    if (rate == null || rate <= 0) rate = 1.0;
    if (rate >= 2.0) rate = 2.0;
    await _methodChannel!.invokeMethod('setRate', {'arg': rate});
    _value = _value.copyWith(rate: rate);
    _notifyChanged();
  }

  /// 设置开始播放时间
  /// [time] 播放时间 单位: ms
  Future<void> setStartTime(int time) async {
    await _methodChannel!.invokeMethod('setStartTime', {'arg': time});
  }

  /// 销毁
  Future<void> dispose() async {
    await _methodChannel!.invokeMethod('dispose', {'arg': _textureId});
    await _playerStateController.close(); // 关闭流
    _playListener = null;
    _methodChannel!.setMethodCallHandler(null);
  }

  /// 原生方法调用 dart 方法监听
  Future<dynamic> _methodHandler(MethodCall call) async {
    final method = call.method;
    switch (method) {
      case "onPrepared":
        // _value = TXVideoPlayerValue.uninitialized();
        final map = _paramsToMap(call.arguments);
        _value = _value.copyWith(
          isInitialized: true,
          isPrepared: true,
          videoWidth: map['width'],
          videoHeight: map['height'],
        );
        break;
      case "onFirstFrame":
        _value = _value.copyWith(showFirstFrame: true, isPlaying: true);
        break;
      case "onPlayProgress":
        final map = _paramsToMap(call.arguments);
        _value = _value.copyWith(
          bufferedDuration: map['buffered'],
          position: map['position'],
          duration: map['duration'],
          isNetDisconnect: false,
          isPlaying: true,
          isPause: false,
          isPlayEnd: false,
        );
        break;
      case "onPlayEnd":
        if (!_value.isPlayEnd) {
          _value = _value.copyWith(isPlayEnd: true, isPlaying: false);
          _notifyChanged();
        }
        break;
      case "onLoading":
        _value = _value.copyWith(isLoading: true, isLoadingEnd: false);
        break;
      case "onLoadingEnd":
        _value = _value.copyWith(isLoadingEnd: true, isLoading: false);
        break;
      case "onNetDisconnect":
        _value = _value.copyWith(isNetDisconnect: true);
        break;
      case "onNetStatus":
        final map = _paramsToMap(call.arguments);
        _value = _value.copyWith(netSpeed: (map['netSpeed'] as int).toDouble());
        break;
    }
    if (method != "onPlayEnd") {
      _notifyChanged();
    }
  }

  void _notifyChanged() {
    if (_playListener != null) {
      _playListener!();
    }
    _playerStateController.add(_value);
  }

  /// 将 native 端的 map 转为 dart 端的 map
  Map<String, dynamic> _paramsToMap(dynamic params) {
    Map paramMap = params as Map;
    Map<String, dynamic> map = <String, dynamic>{};
    if (paramMap.isEmpty) return map;
    paramMap.forEach((key, _value) => map[key] = _value);
    return map;
  }
}
