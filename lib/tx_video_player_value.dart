import 'package:flutter/material.dart';

// author: Liusilong
// date: 2025/3/12
// desc:

class TXVideoPlayerValue {
  // 视频地址
  final String? url;

  // 视频总时长 单位：毫秒
  final int duration;

  // 当前播放的位置 单位：毫秒
  final int position;

  // 当前缓冲的位置 单位：毫秒
  final int bufferedDuration;

  // 视频宽度
  final int videoWidth;

  // 视频高度
  final int videoHeight;

  // 是否正在播放
  final bool isPlaying;

  // 是否循环播放
  final bool isLoop;

  // 是否初始化完成
  final bool isInitialized;

  // 视频是否准备完成
  final bool isPrepared;

  // 是否播放完成
  final bool isPlayEnd;

  // 是否暂停
  final bool? isPause;

  // 是否正在加载中
  final bool isLoading;

  // 是否加载完成
  final bool isLoadingEnd;

  // 网络是否断开连接
  final bool isNetDisconnect;

  // 视频加载网速：kb
  final double netSpeed;

  // 网络接收到首个可渲染的视频数据包
  final bool showFirstFrame;

  // 是否静音
  final bool isMute;

  // 播放倍速
  final double rate;

  TXVideoPlayerValue({
    this.url,
    this.duration = 0,
    this.position = 0,
    this.bufferedDuration = 0,
    this.videoWidth = 0,
    this.videoHeight = 0,
    this.isPlaying = false,
    this.isLoop = false,
    this.isInitialized = false,
    this.isPrepared = false,
    this.isPlayEnd = false,
    this.isPause,
    this.isLoading = false,
    this.isLoadingEnd = false,
    this.isNetDisconnect = false,
    this.netSpeed = 0,
    this.showFirstFrame = false,
    this.isMute = false,
    this.rate = 1.0,
  });

  TXVideoPlayerValue.uninitialized() : this();

  TXVideoPlayerValue copyWith({
    String? url,
    int? duration,
    int? position,
    int? bufferedDuration,
    int? videoWidth,
    int? videoHeight,
    bool? isPlaying,
    bool? isLoop,
    bool? isInitialized,
    bool? isPrepared,
    bool? isPlayEnd,
    bool? isPause,
    bool? isLoading,
    bool? isLoadingEnd,
    bool? isNetDisconnect,
    double? netSpeed,
    bool? showFirstFrame,
    bool? isMute,
    double? rate,
  }) {
    return TXVideoPlayerValue(
      url: url ?? this.url,
      duration: duration ?? this.duration,
      position: position ?? this.position,
      bufferedDuration: bufferedDuration ?? this.bufferedDuration,
      videoWidth: videoWidth ?? this.videoWidth,
      videoHeight: videoHeight ?? this.videoHeight,
      isPlaying: isPlaying ?? this.isPlaying,
      isLoop: isLoop ?? this.isLoop,
      isInitialized: isInitialized ?? this.isInitialized,
      isPrepared: isPrepared ?? this.isPrepared,
      isPlayEnd: isPlayEnd ?? this.isPlayEnd,
      isPause: isPause ?? this.isPause,
      isLoading: isLoading ?? this.isLoading,
      isLoadingEnd: isLoadingEnd ?? this.isLoadingEnd,
      isNetDisconnect: isNetDisconnect ?? this.isNetDisconnect,
      netSpeed: netSpeed ?? this.netSpeed,
      showFirstFrame: showFirstFrame ?? this.showFirstFrame,
      isMute: isMute ?? this.isMute,
      rate: rate ?? this.rate,
    );
  }

  /// 获取视频 宽高比
  double get aspectRatio {
    if (!isInitialized || videoWidth == 0 || videoHeight == 0) {
      return 16 / 9;
    }
    return videoWidth / videoHeight.toDouble();
  }

  @override
  String toString() {
    return '''
    url: $url,
    duration: $duration,
    position: $position,
    bufferedDuration: $bufferedDuration,
    videoWidth: $videoWidth,
    videoHeight: $videoHeight,
    isPlaying: $isPlaying,
    isLoop: $isLoop,
    isInitialized: $isInitialized,
    isPrepared: $isPrepared,
    isPlayEnd: $isPlayEnd,
    isPause: $isPause,
    isLoading: $isLoading,
    isLoadingEnd: $isLoadingEnd,
    isNetDisconnect: $isNetDisconnect,
    netSpeed: $netSpeed,
    showFirstFrame: $showFirstFrame,
    isMute: $isMute,
    rate: $rate,
    ''';
  }
}
