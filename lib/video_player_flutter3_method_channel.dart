import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'video_player_flutter3_platform_interface.dart';

/// An implementation of [VideoPlayerFlutter3Platform] that uses method channels.
class MethodChannelVideoPlayerFlutter3 extends VideoPlayerFlutter3Platform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('video_player_flutter3');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
