import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'video_player_flutter3_method_channel.dart';

abstract class VideoPlayerFlutter3Platform extends PlatformInterface {
  /// Constructs a VideoPlayerFlutter3Platform.
  VideoPlayerFlutter3Platform() : super(token: _token);

  static final Object _token = Object();

  static VideoPlayerFlutter3Platform _instance = MethodChannelVideoPlayerFlutter3();

  /// The default instance of [VideoPlayerFlutter3Platform] to use.
  ///
  /// Defaults to [MethodChannelVideoPlayerFlutter3].
  static VideoPlayerFlutter3Platform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [VideoPlayerFlutter3Platform] when
  /// they register themselves.
  static set instance(VideoPlayerFlutter3Platform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
