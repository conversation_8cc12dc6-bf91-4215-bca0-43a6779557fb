import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:wm_ali_player/video_player_flutter3_method_channel.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  MethodChannelVideoPlayerFlutter3 platform = MethodChannelVideoPlayerFlutter3();
  const MethodChannel channel = MethodChannel('video_player_flutter3');

  setUp(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        return '42';
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(channel, null);
  });

  test('getPlatformVersion', () async {
    expect(await platform.getPlatformVersion(), '42');
  });
}
