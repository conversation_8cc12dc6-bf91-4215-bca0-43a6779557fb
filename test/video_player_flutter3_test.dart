import 'package:flutter_test/flutter_test.dart';
import 'package:wm_ali_player/video_player_flutter3.dart';
import 'package:wm_ali_player/video_player_flutter3_platform_interface.dart';
import 'package:wm_ali_player/video_player_flutter3_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockVideoPlayerFlutter3Platform
    with MockPlatformInterfaceMixin
    implements VideoPlayerFlutter3Platform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final VideoPlayerFlutter3Platform initialPlatform = VideoPlayerFlutter3Platform.instance;

  test('$MethodChannelVideoPlayerFlutter3 is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelVideoPlayerFlutter3>());
  });

  test('getPlatformVersion', () async {
    VideoPlayerFlutter3 videoPlayerFlutter3Plugin = VideoPlayerFlutter3();
    MockVideoPlayerFlutter3Platform fakePlatform = MockVideoPlayerFlutter3Platform();
    VideoPlayerFlutter3Platform.instance = fakePlatform;

    expect(await videoPlayerFlutter3Plugin.getPlatformVersion(), '42');
  });
}
